[debug] [2025-06-09T09:20:51.474Z] ----------------------------------------------------------------------
[debug] [2025-06-09T09:20:51.475Z] Command:       /usr/local/bin/node /Users/<USER>/.nvm/versions/node/v18.20.8/bin/firebase emulators:start --only functions,firestore,auth
[debug] [2025-06-09T09:20:51.476Z] CLI Version:   13.35.1
[debug] [2025-06-09T09:20:51.476Z] Platform:      darwin
[debug] [2025-06-09T09:20:51.476Z] Node Version:  v22.16.0
[debug] [2025-06-09T09:20:51.476Z] Time:          Mon Jun 09 2025 16:20:51 GMT+0700 (Indochina Time)
[debug] [2025-06-09T09:20:51.476Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-06-09T09:20:51.596Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-06-09T09:20:51.597Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-06-09T09:20:51.644Z] openjdk version "11.0.27" 2025-04-15

[debug] [2025-06-09T09:20:51.645Z] OpenJDK Runtime Environment Homebrew (build 11.0.27+0)
OpenJDK 64-Bit Server VM Homebrew (build 11.0.27+0, mixed mode)

[debug] [2025-06-09T09:20:51.647Z] Parsed Java major version: 11
[info] i  emulators: Starting emulators: auth, functions, firestore {"metadata":{"emulator":{"name":"hub"},"message":"Starting emulators: auth, functions, firestore"}}
[debug] [2025-06-09T09:20:52.354Z] [logging] Logging Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-06-09T09:20:52.355Z] [auth] Authentication Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-06-09T09:20:52.355Z] [firestore] Firestore Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-06-09T09:20:52.355Z] [firestore.websocket] websocket server for firestore only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-06-09T09:20:52.355Z] assigned listening specs for emulators {"user":{"hub":[{"address":"127.0.0.1","family":"IPv4","port":4400},{"address":"::1","family":"IPv6","port":4400}],"ui":[{"address":"127.0.0.1","family":"IPv4","port":4000},{"address":"::1","family":"IPv6","port":4000}],"logging":[{"address":"127.0.0.1","family":"IPv4","port":4500}],"auth":[{"address":"127.0.0.1","family":"IPv4","port":9099}],"firestore":[{"address":"127.0.0.1","family":"IPv4","port":8080}],"firestore.websocket":[{"address":"127.0.0.1","family":"IPv4","port":9150}]},"metadata":{"message":"assigned listening specs for emulators"}}
[debug] [2025-06-09T09:20:52.358Z] [hub] writing locator at /var/folders/47/gz90s4vj47d5f3hl6c020lz80000gn/T/hub-qlpbl-b10fc.json
[debug] [2025-06-09T09:20:52.714Z] [functions] Functions Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-06-09T09:20:52.714Z] [eventarc] Eventarc Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-06-09T09:20:52.714Z] [tasks] Cloud Tasks Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-06-09T09:20:52.714Z] late-assigned ports for functions and eventarc emulators {"user":{"hub":[{"address":"127.0.0.1","family":"IPv4","port":4400},{"address":"::1","family":"IPv6","port":4400}],"ui":[{"address":"127.0.0.1","family":"IPv4","port":4000},{"address":"::1","family":"IPv6","port":4000}],"logging":[{"address":"127.0.0.1","family":"IPv4","port":4500}],"auth":[{"address":"127.0.0.1","family":"IPv4","port":9099}],"firestore":[{"address":"127.0.0.1","family":"IPv4","port":8080}],"firestore.websocket":[{"address":"127.0.0.1","family":"IPv4","port":9150}],"functions":[{"address":"127.0.0.1","family":"IPv4","port":5001}],"eventarc":[{"address":"127.0.0.1","family":"IPv4","port":9299}],"tasks":[{"address":"127.0.0.1","family":"IPv4","port":9499}]},"metadata":{"message":"late-assigned ports for functions and eventarc emulators"}}
[warn] ⚠  functions: The following emulators are not running, calls to these services from the Functions emulator will affect production: apphosting, database, hosting, pubsub, storage, dataconnect {"metadata":{"emulator":{"name":"functions"},"message":"The following emulators are not running, calls to these services from the Functions emulator will affect production: \u001b[1mapphosting, database, hosting, pubsub, storage, dataconnect\u001b[22m"}}
