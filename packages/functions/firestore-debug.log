Jun 09, 2025 4:20:57 PM com.google.cloud.datastore.emulator.firestore.websocket.WebSocketServer start
INFO: Started WebSocket server on ws://127.0.0.1:9150
API endpoint: http://127.0.0.1:8080
If you are using a library that supports the FIRESTORE_EMULATOR_HOST environment variable, run:

   export FIRESTORE_EMULATOR_HOST=127.0.0.1:8080

If you are running a Firestore in Datastore Mode project, run:

   export DATASTORE_EMULATOR_HOST=127.0.0.1:8080

Note: Support for Datastore Mode is in preview. If you encounter any bugs please file at https://github.com/firebase/firebase-tools/issues.
Dev App Server is now running.

Jun 09, 2025 4:21:53 PM io.gapi.emulators.netty.HttpVersionRoutingHandler channelRead
INFO: Detected HTTP/2 connection.
Multiple projectIds are not recommended in single project mode. Requested project ID appqlgd, but the emulator is configured for qlpbl-b10fc. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID appqlgd, but the emulator is configured for qlpbl-b10fc. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID appqlgd, but the emulator is configured for qlpbl-b10fc. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID appqlgd, but the emulator is configured for qlpbl-b10fc. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID appqlgd, but the emulator is configured for qlpbl-b10fc. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID appqlgd, but the emulator is configured for qlpbl-b10fc. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID appqlgd, but the emulator is configured for qlpbl-b10fc. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID appqlgd, but the emulator is configured for qlpbl-b10fc. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID appqlgd, but the emulator is configured for qlpbl-b10fc. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Jun 09, 2025 4:26:29 PM com.google.cloud.datastore.emulator.firestore.websocket.WebSocketChannelHandler initChannel
INFO: Connected to new websocket client
Jun 09, 2025 4:26:31 PM com.google.cloud.datastore.emulator.firestore.websocket.WebSocketChannelHandler channelClosed
INFO: Websocket client disconnected
*** shutting down gRPC server since JVM is shutting down
*** server shut down
