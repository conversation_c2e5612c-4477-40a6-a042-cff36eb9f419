# Test info

- Name: Login Page Tests >> should show validation errors for empty fields
- Location: /Users/<USER>/Sale bán lẻ/retail-sales-pulse-ios/packages/web/tests/login.spec.ts:23:3

# Error details

```
Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

Locator: locator('text=Email is required')
Expected: visible
Received: <element(s) not found>
Call log:
  - expect.toBeVisible with timeout 5000ms
  - waiting for locator('text=Email is required')

    at /Users/<USER>/Sale bán lẻ/retail-sales-pulse-ios/packages/web/tests/login.spec.ts:28:58
```

# Page snapshot

```yaml
- region "Notifications (F8)":
  - list:
    - status:
      - img
      - text: Đăng nhập thành công Chào mừng bạn quay trở lại!
      - button:
        - img
- region "Notifications alt+T"
- img "Logo"
- text: <PERSON><PERSON><PERSON> Do<PERSON>h
- button "Mở rộng sidebar":
  - img
- navigation:
  - link "Dashboard Dashboard":
    - /url: /
    - img
    - text: Dashboard Dashboard
  - link "Công việc Công việc":
    - /url: /tasks
    - img
    - text: Công việc Công việc
  - link "Kế hoạch Kế hoạch":
    - /url: /calendar
    - img
    - text: Kế hoạch Kế hoạch
  - link "Báo cáo Báo cáo":
    - /url: /reports
    - img
    - text: Báo cáo Báo cáo
  - link "Nhân viên Nhân viên":
    - /url: /employees
    - img
    - text: Nhân viên Nhân viên
- button "KĐ Khổng Đức Mạnh <EMAIL> Khổng Đức Mạnh":
  - text: KĐ
  - paragraph: Khổng Đức Mạnh
  - paragraph: <EMAIL>
  - text: Khổng Đức Mạnh
- main:
  - heading "Dashboard" [level=1]
  - paragraph: Tổng quan về hiệu suất kinh doanh phòng Kinh doanh bán lẻ
  - button:
    - img
  - button "Xuất báo cáo"
  - text: "Tổng KTS 0 Cũ: 0"
  - img
  - text: 0% so với kế hoạch
  - img
  - text: "Tổng KH/CĐT 0 Cũ: 0"
  - img
  - text: 0% so với kế hoạch
  - img
  - text: "Tổng SBG 0 Cũ: 0"
  - img
  - text: 0% so với kế hoạch
  - img
  - text: "Tổng doanh số 7.23B Cũ: 5.784B"
  - img
  - text: 15.8% so với kế hoạch
  - img
  - heading "Doanh thu" [level=3]
  - group:
    - radio "Tuần"
    - radio "Tháng" [checked]
    - radio "Quý"
  - img: "07000000000140000000002100000000028000000000"
  - list:
    - listitem:
      - img
      - text: Doanh thu
    - listitem:
      - img
      - text: Mục tiêu
  - heading "Nhân viên xuất sắc" [level=3]
  - text: "N"
  - paragraph: Nguyễn Thị Nga
  - paragraph: Nhân viên
  - paragraph: 2580.0tr
  - paragraph: 25 đơn
  - paragraph: 135% mục tiêu
  - text: H
  - paragraph: Phạm Thị Hương
  - paragraph: Nhân viên
  - paragraph: 1310.0tr
  - paragraph: 20 đơn
  - paragraph: 125% mục tiêu
  - text: A
  - paragraph: Lương Việt Anh
  - paragraph: Nhóm trưởng
  - paragraph: 1150.0tr
  - paragraph: 18 đơn
  - paragraph: 120% mục tiêu
  - heading "Phân bố theo vùng" [level=3]
  - img:
    - img
    - img
    - text: 56%44%
  - list:
    - listitem:
      - img
      - text: Hà Nội
    - listitem:
      - img
      - text: TP.HCM
  - heading "Tỷ lệ chuyển đổi" [level=3]
  - text: Báo giá → Đơn hàng 35% KH tiềm năng → KH thực tế 42% KTS tiềm năng → Dự án 28%
- region "Notifications (F8)":
  - list:
    - status:
      - img
      - text: Đăng nhập thành công Chào mừng bạn quay trở lại!
      - button:
        - img
- region "Notifications alt+T"
```

# Test source

```ts
   1 | import { test, expect } from '@playwright/test';
   2 |
   3 | test.describe('Login Page Tests', () => {
   4 |   test.beforeEach(async ({ page }) => {
   5 |     // Điều hướng đến trang đăng nhập
   6 |     await page.goto('http://localhost:8088');
   7 |   });
   8 |
   9 |   test('should display login form elements', async ({ page }) => {
   10 |     // Kiểm tra tiêu đề trang
   11 |     await expect(page).toHaveTitle(/Retail Sales Pulse/);
   12 |     
   13 |     // Kiểm tra các phần tử form đăng nhập có hiển thị
   14 |     await expect(page.locator('input[type="email"]')).toBeVisible();
   15 |     await expect(page.locator('input[type="password"]')).toBeVisible();
   16 |     await expect(page.locator('button[type="submit"]')).toBeVisible();
   17 |     
   18 |     // Kiểm tra placeholder text
   19 |     await expect(page.locator('input[type="email"]')).toHaveAttribute('placeholder', /email/i);
   20 |     await expect(page.locator('input[type="password"]')).toHaveAttribute('placeholder', /password/i);
   21 |   });
   22 |
   23 |   test('should show validation errors for empty fields', async ({ page }) => {
   24 |     // Click nút đăng nhập mà không điền gì
   25 |     await page.locator('button[type="submit"]').click();
   26 |     
   27 |     // Kiểm tra thông báo lỗi validation
>  28 |     await expect(page.locator('text=Email is required')).toBeVisible();
      |                                                          ^ Error: Timed out 5000ms waiting for expect(locator).toBeVisible()
   29 |     await expect(page.locator('text=Password is required')).toBeVisible();
   30 |   });
   31 |
   32 |   test('should show error for invalid email format', async ({ page }) => {
   33 |     // Điền email không hợp lệ
   34 |     await page.locator('input[type="email"]').fill('invalid-email');
   35 |     await page.locator('input[type="password"]').fill('password123');
   36 |     await page.locator('button[type="submit"]').click();
   37 |     
   38 |     // Kiểm tra thông báo lỗi email
   39 |     await expect(page.locator('text=Invalid email format')).toBeVisible();
   40 |   });
   41 |
   42 |   test('should attempt login with valid credentials', async ({ page }) => {
   43 |     // Điền thông tin đăng nhập hợp lệ
   44 |     await page.locator('input[type="email"]').fill('<EMAIL>');
   45 |     await page.locator('input[type="password"]').fill('password123');
   46 |     
   47 |     // Click nút đăng nhập
   48 |     await page.locator('button[type="submit"]').click();
   49 |     
   50 |     // Kiểm tra loading state
   51 |     await expect(page.locator('text=Đang đăng nhập...')).toBeVisible();
   52 |     
   53 |     // Đợi redirect hoặc thông báo
   54 |     await page.waitForTimeout(2000);
   55 |     
   56 |     // Kiểm tra có redirect đến dashboard không
   57 |     const currentUrl = page.url();
   58 |     if (currentUrl.includes('/dashboard')) {
   59 |       console.log('✅ Login successful - redirected to dashboard');
   60 |     } else {
   61 |       console.log('ℹ️ Login form still visible - checking for error messages');
   62 |     }
   63 |   });
   64 |
   65 |   test('should show error for invalid credentials', async ({ page }) => {
   66 |     // Điền thông tin đăng nhập sai
   67 |     await page.locator('input[type="email"]').fill('<EMAIL>');
   68 |     await page.locator('input[type="password"]').fill('wrongpassword');
   69 |     await page.locator('button[type="submit"]').click();
   70 |     
   71 |     // Đợi phản hồi từ server
   72 |     await page.waitForTimeout(3000);
   73 |     
   74 |     // Kiểm tra thông báo lỗi
   75 |     const errorMessage = page.locator('text=Invalid credentials');
   76 |     if (await errorMessage.isVisible()) {
   77 |       console.log('✅ Error message displayed correctly');
   78 |     }
   79 |   });
   80 |
   81 |   test('should toggle password visibility', async ({ page }) => {
   82 |     const passwordInput = page.locator('input[type="password"]');
   83 |     const toggleButton = page.locator('[data-testid="password-toggle"]');
   84 |     
   85 |     // Điền password
   86 |     await passwordInput.fill('testpassword');
   87 |     
   88 |     // Kiểm tra type ban đầu là password
   89 |     await expect(passwordInput).toHaveAttribute('type', 'password');
   90 |     
   91 |     // Click toggle để hiện password
   92 |     if (await toggleButton.isVisible()) {
   93 |       await toggleButton.click();
   94 |       await expect(passwordInput).toHaveAttribute('type', 'text');
   95 |       
   96 |       // Click lại để ẩn password
   97 |       await toggleButton.click();
   98 |       await expect(passwordInput).toHaveAttribute('type', 'password');
   99 |     }
  100 |   });
  101 |
  102 |   test('should take screenshot of login page', async ({ page }) => {
  103 |     // Chụp ảnh màn hình trang đăng nhập
  104 |     await page.screenshot({ 
  105 |       path: 'test-results/login-page.png',
  106 |       fullPage: true 
  107 |     });
  108 |     
  109 |     console.log('📸 Screenshot saved: test-results/login-page.png');
  110 |   });
  111 |
  112 |   test('should test responsive design', async ({ page }) => {
  113 |     // Test trên desktop
  114 |     await page.setViewportSize({ width: 1920, height: 1080 });
  115 |     await page.screenshot({ 
  116 |       path: 'test-results/login-desktop.png',
  117 |       fullPage: true 
  118 |     });
  119 |     
  120 |     // Test trên tablet
  121 |     await page.setViewportSize({ width: 768, height: 1024 });
  122 |     await page.screenshot({ 
  123 |       path: 'test-results/login-tablet.png',
  124 |       fullPage: true 
  125 |     });
  126 |     
  127 |     // Test trên mobile
  128 |     await page.setViewportSize({ width: 375, height: 667 });
```