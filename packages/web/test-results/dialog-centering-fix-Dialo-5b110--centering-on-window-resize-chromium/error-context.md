# Test info

- Name: Dialog Centering Fix Tests >> Dialog should maintain centering on window resize
- Location: /Users/<USER>/Sale bán lẻ/retail-sales-pulse-ios/packages/web/tests/dialog-centering-fix.spec.ts:145:3

# Error details

```
TimeoutError: page.waitForSelector: Timeout 5000ms exceeded.
Call log:
  - waiting for locator('[data-radix-dialog-content]') to be visible

    at /Users/<USER>/Sale bán lẻ/retail-sales-pulse-ios/packages/web/tests/dialog-centering-fix.spec.ts:159:16
```

# Page snapshot

```yaml
- region "Notifications (F8)":
  - list
- region "Notifications alt+T"
- img "Logo"
- heading "Phòng Kinh Doanh Bán Lẻ" [level=1]
- paragraph: Vui lòng đăng nhập để tiếp tục
- img
- text: Khu vực
- combobox: Khổng Đức M<PERSON>nh Trưởng Phòng Kinh doanh bán lẻ
- img
- text: M<PERSON><PERSON> khẩu
- textbox "Nhập mật khẩu": "123456"
- button "<PERSON>ă<PERSON>hập"
- link "Quên mật khẩu?":
  - /url: "#"
  - img
  - text: Quên mật khẩu?
- link "Trợ giúp":
  - /url: "#"
  - img
  - text: Trợ giúp
```

# Test source

```ts
   59 |       console.log('Dialog center:', { x: dialogCenterX, y: dialogCenterY });
   60 |       
   61 |       // Check horizontal centering (allow 10px tolerance)
   62 |       const horizontalDiff = Math.abs(dialogCenterX - viewportCenterX);
   63 |       console.log('Horizontal difference:', horizontalDiff);
   64 |       expect(horizontalDiff).toBeLessThan(10);
   65 |       
   66 |       // Check vertical centering (allow 20px tolerance for header/footer)
   67 |       const verticalDiff = Math.abs(dialogCenterY - viewportCenterY);
   68 |       console.log('Vertical difference:', verticalDiff);
   69 |       expect(verticalDiff).toBeLessThan(20);
   70 |       
   71 |       // Ensure dialog is not at edges
   72 |       expect(dialogBox.x).toBeGreaterThan(10);
   73 |       expect(dialogBox.y).toBeGreaterThan(10);
   74 |       expect(dialogBox.x + dialogBox.width).toBeLessThan(viewportSize.width - 10);
   75 |       expect(dialogBox.y + dialogBox.height).toBeLessThan(viewportSize.height - 10);
   76 |     }
   77 |     
   78 |     // Check CSS properties
   79 |     const computedStyle = await dialog.evaluate((el) => {
   80 |       const style = window.getComputedStyle(el);
   81 |       return {
   82 |         position: style.position,
   83 |         left: style.left,
   84 |         top: style.top,
   85 |         transform: style.transform,
   86 |         zIndex: style.zIndex,
   87 |       };
   88 |     });
   89 |     
   90 |     console.log('Computed style:', computedStyle);
   91 |     
   92 |     expect(computedStyle.position).toBe('fixed');
   93 |     expect(computedStyle.left).toBe('50%');
   94 |     expect(computedStyle.top).toBe('50%');
   95 |     expect(computedStyle.transform).toContain('translate(-50%, -50%)');
   96 |     expect(parseInt(computedStyle.zIndex)).toBeGreaterThanOrEqual(10000);
   97 |   });
   98 |
   99 |   test('Dialog overlay should cover full screen', async ({ page }) => {
  100 |     const buttons = [
  101 |       'button:has-text("Tạo công việc")',
  102 |       'button:has-text("Giao công việc")',
  103 |       'button:has-text("Thêm công việc")'
  104 |     ];
  105 |     
  106 |     for (const buttonSelector of buttons) {
  107 |       const button = page.locator(buttonSelector).first();
  108 |       if (await button.isVisible()) {
  109 |         await button.click();
  110 |         break;
  111 |       }
  112 |     }
  113 |     
  114 |     await page.waitForSelector('[data-radix-dialog-overlay]', { timeout: 5000 });
  115 |     
  116 |     const overlay = page.locator('[data-radix-dialog-overlay]');
  117 |     const overlayBox = await overlay.boundingBox();
  118 |     const viewportSize = page.viewportSize()!;
  119 |     
  120 |     if (overlayBox) {
  121 |       expect(overlayBox.x).toBe(0);
  122 |       expect(overlayBox.y).toBe(0);
  123 |       expect(overlayBox.width).toBe(viewportSize.width);
  124 |       expect(overlayBox.height).toBe(viewportSize.height);
  125 |     }
  126 |     
  127 |     const overlayStyle = await overlay.evaluate((el) => {
  128 |       const style = window.getComputedStyle(el);
  129 |       return {
  130 |         position: style.position,
  131 |         top: style.top,
  132 |         left: style.left,
  133 |         width: style.width,
  134 |         height: style.height,
  135 |         zIndex: style.zIndex,
  136 |       };
  137 |     });
  138 |     
  139 |     expect(overlayStyle.position).toBe('fixed');
  140 |     expect(overlayStyle.top).toBe('0px');
  141 |     expect(overlayStyle.left).toBe('0px');
  142 |     expect(parseInt(overlayStyle.zIndex)).toBeGreaterThanOrEqual(9998);
  143 |   });
  144 |
  145 |   test('Dialog should maintain centering on window resize', async ({ page }) => {
  146 |     const buttons = [
  147 |       'button:has-text("Tạo công việc")',
  148 |       'button:has-text("Giao công việc")'
  149 |     ];
  150 |     
  151 |     for (const buttonSelector of buttons) {
  152 |       const button = page.locator(buttonSelector).first();
  153 |       if (await button.isVisible()) {
  154 |         await button.click();
  155 |         break;
  156 |       }
  157 |     }
  158 |     
> 159 |     await page.waitForSelector('[data-radix-dialog-content]', { timeout: 5000 });
      |                ^ TimeoutError: page.waitForSelector: Timeout 5000ms exceeded.
  160 |     const dialog = page.locator('[data-radix-dialog-content]');
  161 |     
  162 |     // Test different viewport sizes
  163 |     const viewportSizes = [
  164 |       { width: 1920, height: 1080 },
  165 |       { width: 1366, height: 768 },
  166 |       { width: 768, height: 1024 },
  167 |       { width: 375, height: 667 }
  168 |     ];
  169 |     
  170 |     for (const size of viewportSizes) {
  171 |       await page.setViewportSize(size);
  172 |       await page.waitForTimeout(500); // Allow for resize
  173 |       
  174 |       const dialogBox = await dialog.boundingBox();
  175 |       if (dialogBox) {
  176 |         const centerX = dialogBox.x + dialogBox.width / 2;
  177 |         const centerY = dialogBox.y + dialogBox.height / 2;
  178 |         const viewportCenterX = size.width / 2;
  179 |         const viewportCenterY = size.height / 2;
  180 |         
  181 |         const horizontalDiff = Math.abs(centerX - viewportCenterX);
  182 |         const verticalDiff = Math.abs(centerY - viewportCenterY);
  183 |         
  184 |         console.log(`Viewport ${size.width}x${size.height}: H-diff=${horizontalDiff}, V-diff=${verticalDiff}`);
  185 |         
  186 |         expect(horizontalDiff).toBeLessThan(15);
  187 |         expect(verticalDiff).toBeLessThan(25);
  188 |       }
  189 |     }
  190 |   });
  191 |
  192 |   test('Dialog should not be affected by page scroll', async ({ page }) => {
  193 |     // Add some content to make page scrollable
  194 |     await page.evaluate(() => {
  195 |       const div = document.createElement('div');
  196 |       div.style.height = '200vh';
  197 |       div.style.background = 'linear-gradient(red, blue)';
  198 |       document.body.appendChild(div);
  199 |     });
  200 |     
  201 |     // Scroll down
  202 |     await page.evaluate(() => window.scrollTo(0, 500));
  203 |     
  204 |     const buttons = [
  205 |       'button:has-text("Tạo công việc")',
  206 |       'button:has-text("Giao công việc")'
  207 |     ];
  208 |     
  209 |     for (const buttonSelector of buttons) {
  210 |       const button = page.locator(buttonSelector).first();
  211 |       if (await button.isVisible()) {
  212 |         await button.click();
  213 |         break;
  214 |       }
  215 |     }
  216 |     
  217 |     await page.waitForSelector('[data-radix-dialog-content]', { timeout: 5000 });
  218 |     const dialog = page.locator('[data-radix-dialog-content]');
  219 |     
  220 |     const dialogBox = await dialog.boundingBox();
  221 |     const viewportSize = page.viewportSize()!;
  222 |     
  223 |     if (dialogBox) {
  224 |       const centerX = dialogBox.x + dialogBox.width / 2;
  225 |       const centerY = dialogBox.y + dialogBox.height / 2;
  226 |       const viewportCenterX = viewportSize.width / 2;
  227 |       const viewportCenterY = viewportSize.height / 2;
  228 |       
  229 |       // Dialog should still be centered relative to viewport, not page
  230 |       expect(Math.abs(centerX - viewportCenterX)).toBeLessThan(10);
  231 |       expect(Math.abs(centerY - viewportCenterY)).toBeLessThan(20);
  232 |     }
  233 |   });
  234 | });
  235 |
```