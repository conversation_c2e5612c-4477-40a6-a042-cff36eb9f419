import React, { useState, useEffect } from 'react';
import { Plus, X, Briefcase, <PERSON>Pen, FileText, Users, Calendar, Clock } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useAuth } from '@/context/AuthContext';
import { useTaskData } from '@/hooks/use-task-data';
import { useToast } from '@/hooks/use-toast';
import { canAssignTasks } from '@/config/permissions';

interface TaskFormData {
  title: string;
  description: string;
  type: string;
  status: string;
  priority: string;
  date: string;
  time?: string;
  assignedTo?: string;
  isShared?: boolean;
  isSharedWithTeam?: boolean;
}

interface TaskFormDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onTaskCreated?: () => void;
  formType: 'self' | 'team' | 'individual';
}

const TaskFormDialog: React.FC<TaskFormDialogProps> = ({
  open,
  onOpenChange,
  onTaskCreated,
  formType,
}) => {
  const { currentUser, users } = useAuth();
  const { addTask } = useTaskData();
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState<TaskFormData>({
    title: '',
    description: '',
    type: '',
    status: 'todo',
    priority: 'normal',
    date: '',
    time: '',
    assignedTo: currentUser?.id || '',
    isShared: false,
    isSharedWithTeam: false,
  });

  const canAssignToOthers = currentUser && canAssignTasks(currentUser.role);

  const filteredUsers = users.filter((user) => {
    if (formType === 'self') return user.id === currentUser?.id;
    if (formType === 'individual') {
      return currentUser?.team_id === user.team_id;
    }
    return true;
  });

  useEffect(() => {
    if (open) {
      setFormData({
        title: '',
        description: '',
        type: '',
        status: 'todo',
        priority: 'normal',
        date: '',
        time: '',
        assignedTo: currentUser?.id || '',
        isShared: false,
        isSharedWithTeam: false,
      });
    }
  }, [open, currentUser]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.title.trim() || !formData.description.trim() || !formData.type || !formData.date) {
      toast({
        title: 'Lỗi',
        description: 'Vui lòng điền đầy đủ thông tin bắt buộc',
        variant: 'destructive',
      });
      return;
    }

    try {
      setIsSubmitting(true);

      // Tạo task mới
      await addTask({
        title: formData.title,
        description: formData.description,
        type: formData.type,
        status: formData.status as any,
        date: formData.date,
        time: formData.time,
        assignedTo: formData.assignedTo,
        isShared: formData.isShared,
        isSharedWithTeam: formData.isSharedWithTeam,
        priority: formData.priority,
      });

      toast({
        title: 'Thành công',
        description: 'Đã tạo công việc mới thành công',
      });

      // Gọi callback để refresh data
      onTaskCreated?.();
      onOpenChange(false);
    } catch (error) {
      console.error('Error submitting task:', error);
      toast({
        title: 'Lỗi',
        description: error instanceof Error ? error.message : 'Không thể tạo công việc mới',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: keyof TaskFormData, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto bg-white shadow-2xl border-0 relative z-[10000]">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold text-gray-900 flex items-center gap-3">
            <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
              <Plus className="w-4 h-4 text-white" />
            </div>
            {formType === 'self' && 'Tạo công việc mới'}
            {formType === 'team' && 'Giao công việc cho Nhóm'}
            {formType === 'individual' && 'Giao công việc cho thành viên'}
          </DialogTitle>
          <DialogDescription className="text-gray-600">
            {formType === 'self' && 'Tạo công việc cá nhân và quản lý tiến độ'}
            {formType === 'team' && 'Phân công công việc cho nhóm hoặc cá nhân bất kỳ'}
            {formType === 'individual' && 'Phân công công việc cho các thành viên trong nhóm'}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={(e) => { e.preventDefault(); handleSubmit(); }} className="space-y-6 bg-white relative z-10">
          {/* Tiêu đề */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Tiêu đề công việc <span className="text-red-500">*</span>
            </label>
            <Input
              name="title"
              placeholder="Nhập tiêu đề công việc..."
              value={formData.title}
              onChange={(e) => handleInputChange('title', e.target.value)}
              className="w-full bg-white border-gray-300 focus:border-blue-500 focus:ring-blue-500"
              required
            />
          </div>

          {/* Mô tả */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Mô tả chi tiết <span className="text-red-500">*</span>
            </label>
            <Textarea
              name="description"
              placeholder="Mô tả chi tiết về công việc, yêu cầu, mục tiêu..."
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              className="w-full min-h-[100px] bg-white border-gray-300 focus:border-blue-500 focus:ring-blue-500"
              required
            />
          </div>

          {/* Loại công việc */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Loại công việc <span className="text-red-500">*</span>
            </label>
            <Select name="type" value={formData.type} onValueChange={(value) => handleInputChange('type', value)} required>
              <SelectTrigger className="w-full bg-white border-gray-300 focus:border-blue-500 focus:ring-blue-500">
                <SelectValue placeholder="Chọn loại công việc" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="architect_new">KTS mới</SelectItem>
                <SelectItem value="architect_old">KTS cũ</SelectItem>
                <SelectItem value="client_new">KH/CĐT mới</SelectItem>
                <SelectItem value="client_old">KH/CĐT cũ</SelectItem>
                <SelectItem value="quote_new">SBG mới</SelectItem>
                <SelectItem value="quote_old">SBG cũ</SelectItem>
                <SelectItem value="partner_new">Đối tác mới</SelectItem>
                <SelectItem value="partner_old">Đối tác cũ</SelectItem>
                <SelectItem value="other">Công việc khác</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Trạng thái và Ưu tiên */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Trạng thái
              </label>
              <Select value={formData.status} onValueChange={(value) => handleInputChange('status', value)}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Chọn trạng thái" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="todo">Chưa bắt đầu</SelectItem>
                  <SelectItem value="in-progress">Đang thực hiện</SelectItem>
                  <SelectItem value="on-hold">Tạm hoãn</SelectItem>
                  <SelectItem value="completed">Hoàn thành</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Mức độ ưu tiên
              </label>
              <Select value={formData.priority} onValueChange={(value) => handleInputChange('priority', value)}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Chọn mức độ ưu tiên" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="low">Thấp</SelectItem>
                  <SelectItem value="normal">Bình thường</SelectItem>
                  <SelectItem value="high">Cao</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Thời gian */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Ngày thực hiện <span className="text-red-500">*</span>
              </label>
              <Input
                name="date"
                type="date"
                value={formData.date}
                onChange={(e) => handleInputChange('date', e.target.value)}
                className="w-full"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Thời gian (tùy chọn)
              </label>
              <Input
                name="time"
                type="time"
                value={formData.time}
                onChange={(e) => handleInputChange('time', e.target.value)}
                className="w-full"
              />
            </div>
          </div>

          {/* Phân công */}
          {(formType === 'team' || formType === 'individual') && canAssignToOthers && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Giao cho
              </label>
              <Select value={formData.assignedTo} onValueChange={(value) => handleInputChange('assignedTo', value)}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Chọn người thực hiện" />
                </SelectTrigger>
                <SelectContent>
                  {filteredUsers.map((user) => (
                    <SelectItem key={user.id} value={user.id}>
                      {user.name} - {user.location || 'Chưa xác định'}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          {/* Chia sẻ */}
          {formType === 'team' && (
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="shareWithAll"
                  checked={formData.isShared || false}
                  onChange={(e) => handleInputChange('isShared', e.target.checked)}
                  className="rounded"
                />
                <label htmlFor="shareWithAll" className="text-sm text-gray-700">
                  Chia sẻ với tất cả nhân viên
                </label>
              </div>
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="shareWithTeam"
                  checked={formData.isSharedWithTeam || false}
                  onChange={(e) => handleInputChange('isSharedWithTeam', e.target.checked)}
                  className="rounded"
                />
                <label htmlFor="shareWithTeam" className="text-sm text-gray-700">
                  Chia sẻ với nhóm của tôi
                </label>
              </div>
            </div>
          )}
        </form>

        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={() => onOpenChange(false)}
          >
            Hủy bỏ
          </Button>
          <Button
            type="submit"
            onClick={handleSubmit}
            disabled={isSubmitting || !formData.title.trim() || !formData.description.trim() || !formData.type || !formData.date}
          >
            {isSubmitting ? (
              <>
                <div className="w-4 h-4 mr-2 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                Đang lưu...
              </>
            ) : (
              <>
                <Plus className="w-4 h-4 mr-2" />
                {formType === 'self'
                  ? 'Tạo công việc'
                  : formType === 'team'
                    ? 'Giao công việc cho Nhóm'
                    : 'Giao công việc cho thành viên'}
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default TaskFormDialog;
