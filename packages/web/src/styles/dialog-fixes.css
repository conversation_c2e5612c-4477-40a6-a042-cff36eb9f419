/* Dialog positioning and styling fixes */

/* Ensure dialog overlay covers full screen */
[data-radix-dialog-overlay] {
  position: fixed !important;
  inset: 0 !important;
  z-index: 9998 !important;
  background-color: rgba(0, 0, 0, 0.5) !important;
  backdrop-filter: blur(4px) !important;
}

/* Ensure dialog content is perfectly centered */
[data-radix-dialog-content] {
  position: fixed !important;
  left: 50% !important;
  top: 50% !important;
  transform: translate(-50%, -50%) !important;
  z-index: 10000 !important;
  max-height: 85vh !important;
  overflow-y: auto !important;
  border-radius: 16px !important;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;
}

/* Prevent body scroll when dialog is open */
body:has([data-radix-dialog-content]) {
  overflow: hidden !important;
}

/* Smooth animations for dialog */
[data-radix-dialog-content][data-state="open"] {
  animation: dialog-content-show 200ms cubic-bezier(0.16, 1, 0.3, 1) !important;
}

[data-radix-dialog-content][data-state="closed"] {
  animation: dialog-content-hide 200ms cubic-bezier(0.16, 1, 0.3, 1) !important;
}

[data-radix-dialog-overlay][data-state="open"] {
  animation: dialog-overlay-show 200ms cubic-bezier(0.16, 1, 0.3, 1) !important;
}

[data-radix-dialog-overlay][data-state="closed"] {
  animation: dialog-overlay-hide 200ms cubic-bezier(0.16, 1, 0.3, 1) !important;
}

@keyframes dialog-content-show {
  from {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

@keyframes dialog-content-hide {
  from {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
  to {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.95);
  }
}

@keyframes dialog-overlay-show {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes dialog-overlay-hide {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

/* Task form specific styling */
.task-form-dialog {
  width: 100% !important;
  max-width: 600px !important;
  margin: 0 auto !important;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  [data-radix-dialog-content] {
    width: 95vw !important;
    max-width: 95vw !important;
    max-height: 90vh !important;
    margin: 0 !important;
  }
  
  .task-form-dialog {
    max-width: 95vw !important;
  }
}

/* Form input styling improvements */
.task-form-dialog input,
.task-form-dialog textarea,
.task-form-dialog select {
  transition: all 0.2s ease-in-out !important;
}

.task-form-dialog input:focus,
.task-form-dialog textarea:focus,
.task-form-dialog select:focus {
  outline: none !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

/* Button styling improvements */
.task-form-dialog button {
  transition: all 0.2s ease-in-out !important;
}

.task-form-dialog button:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

.task-form-dialog button:active {
  transform: translateY(0) !important;
}

/* Close button positioning */
[data-radix-dialog-close] {
  position: absolute !important;
  right: 16px !important;
  top: 16px !important;
  z-index: 10 !important;
  opacity: 0.7 !important;
  transition: opacity 0.2s ease-in-out !important;
}

[data-radix-dialog-close]:hover {
  opacity: 1 !important;
}
