#!/usr/bin/env node

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Khởi động MCP Server Playwright...');

// C<PERSON>u hình MCP server
const config = {
  port: 3001,
  host: 'localhost',
  headless: true,
  capabilities: ['tabs', 'pdf', 'history', 'wait', 'files'],
  outputDir: './test-results',
  saveTrace: true
};

// Tạo thư mục output nếu chưa có
const outputDir = path.resolve(config.outputDir);
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
  console.log(`📁 Đã tạo thư mục output: ${outputDir}`);
}

// Xây dựng lệnh khởi động
const args = [
  'mcp-server-playwright',
  '--port', config.port.toString(),
  '--host', config.host,
  '--caps', config.capabilities.join(','),
  '--output-dir', config.outputDir,
  '--save-trace'
];

if (config.headless) {
  args.push('--headless');
}

console.log(`🔧 Lệnh khởi động: npx ${args.join(' ')}`);

// Khởi động MCP server
const mcpServer = spawn('npx', args, {
  stdio: 'inherit',
  cwd: process.cwd()
});

// Xử lý sự kiện
mcpServer.on('spawn', () => {
  console.log('✅ MCP Server đã khởi động thành công!');
  console.log(`🌐 Server URL: http://${config.host}:${config.port}`);
  console.log(`📊 SSE Endpoint: http://[::1]:${config.port}/sse`);
  console.log(`🔗 MCP Endpoint: http://[::1]:${config.port}/mcp`);
  console.log('📝 Trace viewer sẽ khả dụng sau khi có test chạy');
  
  // Cập nhật file cấu hình
  const configPath = path.resolve('./mcp-config.json');
  const mcpConfig = {
    mcpServers: {
      playwright: {
        url: `http://[::1]:${config.port}/sse`,
        description: "Playwright MCP Server for automated testing",
        capabilities: config.capabilities,
        config: config
      }
    },
    endpoints: {
      sse: `http://[::1]:${config.port}/sse`,
      mcp: `http://[::1]:${config.port}/mcp`,
      traceViewer: "http://localhost:49997/trace/index.html?trace=test-results/traces/trace.json"
    },
    status: "running",
    startedAt: new Date().toISOString()
  };
  
  fs.writeFileSync(configPath, JSON.stringify(mcpConfig, null, 2));
  console.log(`💾 Đã cập nhật cấu hình: ${configPath}`);
});

mcpServer.on('error', (error) => {
  console.error('❌ Lỗi khi khởi động MCP Server:', error);
  process.exit(1);
});

mcpServer.on('exit', (code) => {
  console.log(`🔚 MCP Server đã dừng với mã: ${code}`);
  
  // Cập nhật trạng thái trong config
  const configPath = path.resolve('./mcp-config.json');
  if (fs.existsSync(configPath)) {
    const mcpConfig = JSON.parse(fs.readFileSync(configPath, 'utf8'));
    mcpConfig.status = 'stopped';
    mcpConfig.stoppedAt = new Date().toISOString();
    fs.writeFileSync(configPath, JSON.stringify(mcpConfig, null, 2));
  }
});

// Xử lý tín hiệu dừng
process.on('SIGINT', () => {
  console.log('\n🛑 Đang dừng MCP Server...');
  mcpServer.kill('SIGINT');
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Đang dừng MCP Server...');
  mcpServer.kill('SIGTERM');
});
