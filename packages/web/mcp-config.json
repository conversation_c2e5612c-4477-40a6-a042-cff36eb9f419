{"mcpServers": {"playwright": {"url": "http://[::1]:3001/sse", "description": "Playwright MCP Server for automated testing", "capabilities": ["tabs", "pdf", "history", "wait", "files"], "config": {"headless": true, "outputDir": "./test-results", "saveTrace": true, "port": 3001, "host": "localhost"}}}, "endpoints": {"sse": "http://[::1]:3001/sse", "mcp": "http://[::1]:3001/mcp", "traceViewer": "http://localhost:49997/trace/index.html?trace=test-results/traces/trace.json"}, "status": "running", "startedAt": "2024-12-19T10:00:00Z"}