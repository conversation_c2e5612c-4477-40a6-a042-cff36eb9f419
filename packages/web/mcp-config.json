{"mcpServers": {"playwright": {"url": "http://localhost:3001/sse", "description": "Playwright MCP Server for automated testing", "capabilities": ["tabs", "wait", "files"], "config": {"headless": true, "outputDir": "./test-results", "port": 3001, "host": "0.0.0.0"}}, "augment": {"url": "http://localhost:3002/sse", "description": "Augment MCP Server with AI-powered features", "capabilities": ["analyze_code", "manage_tasks", "generate_component", "query_database", "monitor_performance"], "config": {"port": 3002, "host": "localhost", "features": {"codeAnalysis": true, "taskManagement": true, "componentGeneration": true, "databaseQuery": true, "performanceMonitoring": true}}}}, "endpoints": {"playwright": {"sse": "http://localhost:3001/sse", "mcp": "http://localhost:3001/mcp", "server": "http://localhost:3001"}, "augment": {"sse": "http://localhost:3002/sse", "mcp": "http://localhost:3002/mcp", "server": "http://localhost:3002"}}, "status": "running", "startedAt": "2024-12-19T14:45:00Z", "commands": {"playwright": {"start": "npx @playwright/mcp --port 3001 --host 0.0.0.0 --headless --caps tabs,wait,files --output-dir ./test-results", "startScript": "npm run mcp:start", "status": "npm run mcp:status"}, "augment": {"start": "node scripts/augment-mcp-server.js", "startScript": "npm run augment:start", "status": "npm run augment:status", "dev": "npm run augment:dev"}, "all": {"start": "npm run mcp:all", "dev": "npm run mcp:dev"}}}