{"mcpServers": {"playwright": {"url": "http://localhost:3001/sse", "description": "Playwright MCP Server for automated testing", "capabilities": ["tabs", "wait", "files"], "config": {"headless": true, "outputDir": "./test-results", "port": 3001, "host": "0.0.0.0"}}}, "endpoints": {"sse": "http://localhost:3001/sse", "mcp": "http://localhost:3001/mcp", "server": "http://localhost:3001"}, "status": "running", "startedAt": "2024-12-19T14:45:00Z", "commands": {"start": "npx @playwright/mcp --port 3001 --host 0.0.0.0 --headless --caps tabs,wait,files --output-dir ./test-results", "startScript": "npm run mcp:start", "status": "npm run mcp:status"}}